import React from "react";

import logo from "../images/anas.jpg";
const Navbar = () => {
  //add the active class

  return (
    <>
      <div>
        <header className="header">
          <div className="header__content">
            <div className="header__logo-container">
              <div className="header__logo-img-cont">
                <img src={logo} alt="logo" className="header__logo-img" />
              </div>
              <span className="header__logo-sub"><PERSON></span>
            </div>
            <div className="header__main">
              <ul id="#navbar" className="header__links">
                <li className="header__link-wrapper">
                  <a href="index.html" className="header__link active">
                    Home
                  </a>
                </li>
                <li className="header__link-wrapper">
                  <a href="index.html#about" className="header__link active">
                    About
                  </a>
                </li>
                <li className="header__link-wrapper">
                  <a href="index.html#resume" className="header__link active">
                    Experience & Education
                  </a>
                </li>
                <li className="header__link-wrapper">
                  <a href="index.html#projects" className="header__link active">
                    Projects
                  </a>
                </li>
                <li className="header__link-wrapper">
                  <a href="index.html#contact" className="header__link active">
                    Contact
                  </a>
                </li>
              </ul>
              <div className="header__main-ham-menu-cont">{/* hAm */}</div>
            </div>
          </div>
        </header>
      </div>
    </>
  );
};

export default Navbar;

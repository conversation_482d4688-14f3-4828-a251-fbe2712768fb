*,
::after,
::before {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
  font-family: inherit;
}
html {
  font-size: 62.5%;
  scroll-behavior: smooth;
}
@media only screen and (max-width: 75em) {
  html {
    font-size: 59%;
  }
}
@media only screen and (max-width: 56.25em) {
  html {
    font-size: 56%;
  }
}
@media only screen and (min-width: 112.5em) {
  html {
    font-size: 65%;
  }
}
body {
  box-sizing: border-box;
  position: relative;
  line-height: 1.5;
  font-family: sans-serif;
  overflow-x: hidden;
  overflow-y: scroll;
  font-family: "Source Sans Pro", sans-serif;
}
a {
  text-decoration: none;
  color: inherit;
}
a:hover {
  color: #fbff12;
  background-color: black;
}
li {
  list-style: none;
}
a:focus,
button:focus,
input:focus,
textarea:focus {
  outline: 0;
}
button {
  border: none;
  cursor: pointer;
}
textarea {
  resize: none;
}
/* Home Title */
.heading-primary {
  font-size: 5rem;
  text-transform: uppercase;
  letter-spacing: 3px;
  text-align: center;
  color: white;
  font-weight: 800;
}
@media only screen and (max-width: 37.5em) {
  .heading-primary {
    font-size: 4.5rem;
  }
}
.heading-sec__mb-bg {
  margin-bottom: 11rem;
}
@media only screen and (max-width: 56.25em) {
  .heading-sec__mb-bg {
    margin-bottom: 8rem;
  }
}
.heading-sec__mb-med {
  margin-bottom: 9rem;
}
@media only screen and (max-width: 56.25em) {
  .heading-sec__mb-med {
    margin-bottom: 8rem;
  }
}
/* About Heading  */
.heading-sec__main {
  display: block;
  font-size: 4rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  letter-spacing: 3px;
  text-align: center;
  margin-bottom: 3.5rem;
  position: relative;
  color: white;
}
/* title End */
/* Contact Heading */
.heading-sec__main--lt {
  color: white;
  font-weight: 800;
}
.heading-sec__main--lt::after {
  content: "";
  background: linear-gradient(
    to left,
    rgba(12, 10, 10, 0.8),
    rgba(214, 211, 0, 0.8)
  );
}
.btn {
  background: yellow;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: inline-block;
  font-weight: 700;
  border-radius: 5px;
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15);
  transition: transform 0.3s;
}
.btn:hover {
  transform: translateY(-3px);
  background-color: #90ee90;
}
.btn--bg {
  padding: 1.5rem 8rem;
  font-size: 2rem;
}
.btn--med {
  padding: 1.5rem 5rem;
  font-size: 1.6rem;
}
.btn--theme {
  background: linear-gradient(rgba(214, 211, 0, 0.8));
  color: black;
}
.btn--theme-inv {
  color: #111;
  background: #fff;
  border: 2px solid #7843e9;
  box-shadow: none;
  padding: calc(1.5rem - 2px) calc(5rem - 2px);
}
.heading-sec__sub--lt {
  color: #333;
}
.heading-sec__main::after {
  content: "";
  position: absolute;
  top: calc(100% + 1.5rem);
  height: 5px;
  width: 3rem;
  background: yellow;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 5px;
}
@media only screen and (max-width: 37.5em) {
  .heading-sec__main::after {
    top: calc(100% + 1.2rem);
  }
}
.heading-sec__sub {
  display: block;
  text-align: center;
  color: white;
  font-size: 2rem;
  font-weight: 500;
  max-width: 80rem;
  margin: auto;
  line-height: 1.6;
}
@media only screen and (max-width: 37.5em) {
  .heading-sec__sub {
    font-size: 1.8rem;
  }
}

/* Footer  */
.heading-sm {
  font-size: 2.2rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.main-container {
  max-width: 120rem;
  margin: auto;
  width: 92%;
}

.sec-pad {
  padding: 12rem 0;
  background: black;
  box-shadow: 0 0 5px white, 0 0 10px white;
}
@media only screen and (max-width: 56.25em) {
  .sec-pad {
    padding: 8rem 0;
  }
}
.text-primary {
  color: white;
  font-size: 2.2rem;
  text-align: center;
  width: 100%;
  line-height: 1.6;
}
@media only screen and (max-width: 37.5em) {
  .text-primary {
    font-size: 2rem;
  }
}
.d-none {
  display: none;
}
/* Home */
.home-hero {
  color: #ffffff;
  background: black;
  background-position: center;
  height: 100vh;
  min-height: 80rem;
  max-height: 120rem;
  position: relative;
  box-shadow: 0 0 5px white, 0 0 10px white;

  background-repeat: no-repeat;
  background-size: cover;
}
@media only screen and (max-width: 37.5em) {
  .home-hero {
    height: unset;
    min-height: unset;
  }
}
.home-hero__socials {
  position: absolute;
  color: white;
  top: 50%;
  transform: translateY(-50%);
  background: yellow;
  box-shadow: rgba(41, 41, 160, 0.2) 0 7px 29px 0;
  padding: 0.5rem;
}

@media only screen and (max-width: 56.25em) {
  .home-hero__socials {
    display: none;
  }
}
.home-hero__mouse-scroll-cont {
  position: absolute;
  bottom: 3%;
  left: 50%;
  background: yellow;
  border-radius: 12px;
  transform: translateX(-50%);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -ms-border-radius: 12px;
  -o-border-radius: 12px;
}
@media only screen and (max-width: 37.5em) {
  .home-hero__mouse-scroll-cont {
    display: none;
  }
}
.home-hero__social {
  width: 5rem;
}
.home-hero__social:hover {
  background-color: rgb(99, 255, 125);
}
.home-hero__social-icon-link {
  width: 100%;
  display: block;
  padding: 1rem;
  transition: background 0.3s;
  border-radius: 5px;
}
.home-hero__social-icon-link:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 5px yellow, 0 0 25px yellow, 0 0 50px yellow, 0 0 100px yellow,
    0 0 200px yellow;
}
.home-hero__social-icon-link--bd-none {
  border-bottom: 0;
}
.home-hero__social-icon {
  width: 100%;
}
.home-hero__content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90rem;
  width: 92%;
}
@media only screen and (max-width: 37.5em) {
  .home-hero__content {
    padding: 19rem 0 13rem 0;
    margin: auto;
    position: static;
    transform: translate(0, 0);
  }
}
.home-hero__info {
  margin: 3rem auto 0 auto;
  max-width: 80rem;
}
.home-hero__cta {
  margin-top: 5rem;
  text-align: center;
}

/* About Section */
.about {
  background: black;
  box-shadow: 0 0 5px white, 0 0 10px white;
}
.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10rem;
}
@media only screen and (max-width: 56.25em) {
  .about__content {
    grid-template-columns: 1fr;
    grid-gap: 8rem;
  }
}
.about__content-title {
  font-weight: 700;
  font-size: 2.8rem;
  margin-bottom: 3rem;
  color: white;
}
@media only screen and (max-width: 37.5em) {
  .about__content-title {
    font-size: 2.4rem;
  }
}
.about__content-details-para {
  font-size: 1.8rem;
  color: white;
  max-width: 60rem;
  line-height: 1.7;
  margin-bottom: 1rem;
}
.about__content-details-para--hl {
  font-weight: 700;
  margin: 0 3px;
}
.about__content-details-para:last-child {
  margin-bottom: 4rem;
}
.about__content-details-para a {
  text-decoration: underline;
  font-weight: 700;
  color: #7843e9;
  margin: 0 3px;
}

/* Resume Section */
.resume {
  background: black;
  box-shadow: 0 0 5px white, 0 0 10px white;
}

.resume__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 8rem;
  margin-top: 6rem;
}

@media only screen and (max-width: 56.25em) {
  .resume__content {
    grid-template-columns: 1fr;
    grid-gap: 6rem;
  }
}

.resume__section-header {
  display: flex;
  align-items: center;
  margin-bottom: 4rem;
}

.resume__icon {
  width: 5rem;
  height: 5rem;
  background: yellow;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2rem;
  color: black;
}

.resume__section-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@media only screen and (max-width: 37.5em) {
  .resume__section-title {
    font-size: 2.4rem;
  }
}

.resume__timeline {
  position: relative;
}

.resume__item {
  background: linear-gradient(
    to left,
    rgba(12, 10, 10, 0.8),
    rgba(214, 211, 0, 0.8)
  );
  padding: 3rem;
  border-radius: 8px;
  margin-bottom: 3rem;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.resume__item:last-child {
  margin-bottom: 0;
}

.resume__item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

@media only screen and (max-width: 37.5em) {
  .resume__item-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

.resume__item-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: yellow;
  margin: 0;
}

@media only screen and (max-width: 37.5em) {
  .resume__item-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
}

.resume__item-date {
  font-size: 1.4rem;
  color: #ccc;
  font-weight: 600;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.resume__item-company {
  font-size: 1.8rem;
  font-weight: 600;
  color: white;
  margin: 0 0 1.5rem 0;
}

@media only screen and (max-width: 37.5em) {
  .resume__item-company {
    font-size: 1.6rem;
  }
}

.resume__item-description {
  font-size: 1.6rem;
  color: #ddd;
  line-height: 1.6;
  margin: 0;
}

@media only screen and (max-width: 37.5em) {
  .resume__item-description {
    font-size: 1.5rem;
  }
}

/* Project Sec */

.projects__row {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  grid-gap: 5rem;
  margin-bottom: 11rem;
  background: linear-gradient(
    to left,
    rgba(12, 10, 10, 0.8),
    rgba(214, 211, 0, 0.8)
  );
  color: white;
}
@media only screen and (max-width: 56.25em) {
  .projects__row {
    grid-template-columns: 1fr;
    grid-gap: 2rem;
    margin-bottom: 8rem;
  }
}
@media only screen and (max-width: 56.25em) {
  .projects__row {
    text-align: center;
  }
}
.projects__row:last-child {
  margin-bottom: 0;
}
.projects__row-img-cont {
  overflow: hidden;
}
.projects__row-img {
  width: 100%;
  display: block;
  -o-object-fit: cover;
  object-fit: cover;
}
.projects__row-content {
  padding: 2rem 0;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: flex-start;
}
@media only screen and (max-width: 56.25em) {
  .projects__row-content {
    align-items: center;
  }
}
.projects__row-content-title {
  font-weight: 700;
  font-size: 2.8rem;
  margin-bottom: 2rem;
}
@media only screen and (max-width: 37.5em) {
  .projects__row-content-title {
    font-size: 2.4rem;
  }
}
.projects__row-content-desc {
  font-size: 1.8rem;
  color: #ffffff;
  max-width: 60rem;
  line-height: 1.7;
  margin-bottom: 3rem;
}
@media only screen and (max-width: 37.5em) {
  .projects__row-content-desc {
    font-size: 1.7rem;
  }
}

/* Contact */
.contact {
  background: black;
  background-size: cover;
  background-position: center;
  box-shadow: 0 0 5px white, 0 0 10px white;
}
.contact__form-container {
  box-shadow: 0 0 5px white, 0 0 10px white;
  background: black;
  padding: 4rem;
  margin-top: 6rem;
  max-width: 80rem;
  text-align: right;
  width: 95%;
  border-radius: 5px;
  margin: 5rem auto 0 auto;
}
@media only screen and (max-width: 37.5em) {
  .contact__form-container {
    padding: 3rem;
  }
}
.contact__form-field {
  margin-bottom: 4rem;
}
@media only screen and (max-width: 37.5em) {
  .contact__form-field {
    margin-bottom: 3rem;
  }
}
.contact__form-label {
  color: #666;
  font-size: 1.4rem;
  letter-spacing: 1px;
  font-weight: 700;
  margin-bottom: 1rem;
  display: block;
  text-align: left;
}
.contact__form-input {
  color: #333;
  padding: 2rem;
  width: 100%;
  border: 1px solid #ebebeb;
  font-size: 1.6rem;
  letter-spacing: 0;
  background: #f0f0f0;
  border-radius: 5px;
  font-weight: 600;
}
.contact__form-input::-moz-placeholder {
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__form-input:-ms-input-placeholder {
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__form-input::placeholder {
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__form-input::-webkit-input-placeholder {
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__form-input:-ms-input-placeholder {
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__form-input::-ms-input-placeholder {
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__form-input:-moz-placeholder {
  opacity: 1;
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__form-input::-moz-placeholder {
  opacity: 1;
  color: #999;
  font-weight: 600;
  font-size: 1.6rem;
}
.contact__btn {
  width: 30%;
  padding: 2rem 4rem;
  font-size: 1.6rem;
}
@media only screen and (max-width: 37.5em) {
  .contact__btn {
    width: 100%;
  }
}
/* End */

.project-cs-hero {
  color: #ff0000;
  background: linear-gradient(
    to right,
    rgba(245, 245, 245, 0.8),
    rgba(245, 245, 245, 0.8)
  );
  background-size: cover;
  background-position: center;
  position: relative;
}
@media only screen and (max-width: 37.5em) {
  .project-cs-hero {
    height: unset;
    min-height: unset;
  }
}
.project-cs-hero__content {
  padding: 25rem 0 17rem 0;
  max-width: 90rem;
  width: 92%;
  margin: auto;
}
@media only screen and (max-width: 37.5em) {
  .project-cs-hero__content {
    padding: 19rem 0 13rem 0;
    margin: auto;
    position: static;
    transform: translate(0, 0);
  }
}
.project-cs-hero__info {
  margin: 3rem auto 0 auto;
  max-width: 80rem;
}
.project-cs-hero__cta {
  margin-top: 5rem;
  text-align: center;
}
.project-details__content {
  padding: 8rem 0;
  max-width: 90rem;
  margin: auto;
}
.project-details__content-title {
  font-weight: 700;
  font-size: 2.8rem;
  margin-bottom: 3rem;
}
@media only screen and (max-width: 37.5em) {
  .project-details__content-title {
    font-size: 2.4rem;
  }
}
.project-details__showcase-img-cont {
  width: 100%;
  margin-bottom: 6rem;
}
.project-details__showcase-img {
  width: 100%;
}
.project-details__content-main {
  width: 100%;
  max-width: 70rem;
  margin: auto;
}
.project-details__desc {
  margin: 0 0 7rem 0;
}
.project-details__desc-para {
  font-size: 1.8rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 2rem;
}
.project-details__tools-used {
  margin: 0 0 7rem 0;
}
.project-details__tools-used-list {
  display: flex;
  flex-wrap: wrap;
}
.project-details__tools-used-item {
  padding: 1rem 2rem;
  margin-bottom: 1.5rem;
  margin-right: 1.5rem;
  font-size: 1.6rem;
  background: rgba(153, 153, 153, 0.2);
  border-radius: 5px;
  font-weight: 600;
  color: #777;
}
.project-details__links {
  margin: 0 0;
}
.project-details__links-btn {
  margin-right: 2rem;
}
@media only screen and (max-width: 37.5em) {
  .project-details__links-btn {
    margin-right: 0;
    width: 70%;
    margin-bottom: 2rem;
    text-align: center;
  }
}
.project-details__links-btn:last-child {
  margin: 0;
}
@media only screen and (max-width: 37.5em) {
  .project-details__links-btn:last-child {
    margin: 0;
  }
}

/* Header */
.header {
  position: fixed;
  width: 100%;
  z-index: 1000;

  background-color: rgba(0, 0, 0, 0.3);

  box-shadow: 0 10px 100px rgba(0, 0, 0, 0.1);
}
.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 5rem;
}
@media only screen and (max-width: 56.25em) {
  .header__content {
    padding: 0 2rem;
  }
}
.header__logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;

  transition: color 0.3s;
}

.header__logo-img-cont {
  width: 5rem;
  height: 5rem;
  border-radius: 50px;
  overflow: hidden;
  margin-right: 1.5rem;
  background: #7843e9;
}
@media only screen and (max-width: 56.25em) {
  .header__logo-img-cont {
    width: 4.5rem;
    height: 4.5rem;
    margin-right: 1.2rem;
  }
}
.header__logo-img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
  display: block;
}
.header__logo-sub {
  font-size: 1.8rem;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 1px;
  color: yellow;
}
.header__logo-sub:hover {
  background-color: yellow;
  color: black;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}
.header__links {
  display: flex;
}
@media only screen and (max-width: 37.5em) {
  .header__links {
    display: none;
  }
}
.header__link {
  padding: 2.2rem 3rem;
  display: inline-block;
  font-size: 1.6rem;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
  transition: color 0.3s;
  color: yellow;
}
.header__link:hover {
  color: black;
  background-color: yellow;
  border-radius: 40px;
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -ms-border-radius: 40px;
  -o-border-radius: 40px;
}
@media only screen and (max-width: 56.25em) {
  .header__link {
    padding: 3rem 1.8rem;
    font-size: 1.5rem;
  }
}
.header__main-ham-menu-cont {
  display: none;
  width: 3rem;
  padding: 2.2rem 0;
  color: yellow;
  font-size: 2.8rem;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
}

@media only screen and (max-width: 37.5em) {
  .header__main-ham-menu-cont {
    display: block;
  }
}
.header__main-ham-menu {
  width: 100%;
}
.header__main-ham-menu-close {
  width: 100%;
}
.header__sm-menu {
  background: #fff;
  position: absolute;
  width: 100%;
  top: 100%;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
  box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
}
.header__sm-menu--active {
  visibility: hidden;
  opacity: 0;
}
@media only screen and (max-width: 37.5em) {
  .header__sm-menu--active {
    visibility: visible;
    opacity: 1;
  }
}
.header__sm-menu-link a {
  display: block;
  text-decoration: none;
  padding: 2.5rem 3rem;
  font-size: 1.6rem;
  color: #333;
  text-align: right;
  border-bottom: 1px solid #eee;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  transition: color 0.3s;
}
.header__sm-menu-link a:hover {
  color: #ffffff;
}
.header__sm-menu-link:first-child a {
  border-top: 1px solid #eee;
}
.header__sm-menu-link-last {
  border-bottom: 0;
}

/* Footer */
.main-footer {
  background: black;
  color: white;
  box-shadow: 0 0 5px white, 0 0 10px white;
}
.main-footer__upper {
  display: flex;
  justify-content: space-between;
  padding: 8rem 0;
}
@media only screen and (max-width: 56.25em) {
  .main-footer__upper {
    padding: 6rem 0;
  }
}
@media only screen and (max-width: 37.5em) {
  .main-footer__upper {
    display: block;
  }
}
.main-footer__row-1 {
  order: 2;
}
@media only screen and (max-width: 56.25em) {
  .main-footer__row-1 {
    margin-bottom: 5rem;
  }
}
.main-footer__row-2 {
  width: 40%;
  order: 1;
  max-width: 50rem;
}
@media only screen and (max-width: 56.25em) {
  .main-footer__row-2 {
    width: 100%;
  }
}
.main-footer__short-desc {
  margin-top: 2rem;
  color: white;
  font-size: 1.5rem;
  line-height: 1.7;
}
.main-footer__social-cont {
  margin-top: 2rem;
}
.main-footer__icon {
  margin-right: 1rem;
  width: 2.5rem;
  background-color: yellow;
}
.main-footer__icon--mr-none {
  margin-right: 0;
}
.main-footer__lower {
  padding: 4rem 0;
  border-top: 1px solid #444;
  color: white;
  font-size: 1.2rem;
  text-align: left;
  text-align: center;
}
.main-footer__lower a {
  text-decoration: underline;
  font-weight: 700;
  margin-left: 2px;
}
@media only screen and (max-width: 56.25em) {
  .main-footer__lower {
    padding: 3.5rem 0;
  }
}
.skills {
  display: flex;
  flex-wrap: wrap;
}
.skills__skill {
  padding: 1rem 2rem;
  margin-bottom: 1.5rem;
  margin-right: 1.5rem;
  font-size: 1.6rem;
  background: yellow;
  border-radius: 5px;
  font-weight: 600;
  color: #000000;
}
.mouse {
  width: 25px;
  height: 40px;
  border: 2px solid #333;
  border-radius: 60px;
  position: relative;
  overflow: hidden;
}
.mouse::before {
  content: "";
  width: 5px;
  height: 5px;
  position: absolute;

  top: 7px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  border-radius: 50%;
  opacity: 1;
  animation: wheel 1.3s infinite;
  -webkit-animation: wheel 1.3s infinite;
}
@keyframes wheel {
  to {
    opacity: 0;
    top: 27px;
  }
}
@-webkit-keyframes wheel {
  to {
    opacity: 0;
    top: 27px;
  }
}

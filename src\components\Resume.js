import React from "react";

const Resume = () => {
  return (
    <>
      <section id="resume" className="resume sec-pad">
        <div className="main-container">
          <h2 className="heading heading-sec heading-sec__mb-bg">
            <span className="heading-sec__main">Experience & Education</span>
            <span className="heading-sec__sub">
              My professional experience and educational background
            </span>
          </h2>
          <div className="resume__content">
            <div className="resume__section">
              <div className="resume__section-header">
                <div className="resume__icon">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M20 6H16V4C16 2.89 15.11 2 14 2H10C8.89 2 8 2.89 8 4V6H4C2.89 6 2.01 6.89 2.01 8L2 19C2 20.11 2.89 21 4 21H20C21.11 21 22 20.11 22 19V8C22 6.89 21.11 6 20 6ZM10 4H14V6H10V4ZM20 19H4V8H20V19Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <h3 className="resume__section-title">Experience</h3>
              </div>
              <div className="resume__timeline">
                <div className="resume__item">
                  <div className="resume__item-header">
                    <h4 className="resume__item-title">Internee</h4>
                  </div>
                  <h5 className="resume__item-company">
                    Regex Byte IT Solution and Consultancy
                  </h5>
                  <p className="resume__item-description">
                    Dynamic and driven MERN Stack Developer with hands-on
                    experience gained through an internship at Regex Byte.
                    Proficient in developing robust web applications using
                    MongoDB, Express.js, React.js, and Node.js. Eager to
                    leverage skills and knowledge in a challenging professional
                    environment to contribute to innovative projects and drive
                    organizational success.
                  </p>
                  <h4 className="resume__item-company">
                    The technologies I worked on during internship were Html,
                    Css, Bootstrap, React, Node, Express and MySQL.
                  </h4>
                </div>
                <div className="resume__item">
                  <div className="resume__item-header">
                    <h4 className="resume__item-title">Software Eng Node</h4>
                    <span className="resume__item-date">March 2024</span>
                  </div>
                  <h5 className="resume__item-company">
                    Sentrix Technologies LLC
                  </h5>
                  <p className="resume__item-description">
                    I am currently working with Node.js and MySQL technologies.
                    My focus is on building efficient and scalable applications.
                    Architected and implemented robust backend solutions,
                    ensuring high performance and reliability. Collaborated with
                    crossfunctional teams to design and implement RESTful APIs
                    for data integration.
                  </p>
                </div>
              </div>
            </div>

            <div className="resume__section">
              <div className="resume__section-header">
                <div className="resume__icon">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5 13.18V4H12V2H5C3.9 2 3 2.9 3 4V13.18C2.4 13.6 2 14.3 2 15V20C2 21.1 2.9 22 4 22H20C21.1 22 22 21.1 22 20V15C22 14.3 21.6 13.6 21 13.18V4C21 2.9 20.1 2 19 2H14V4H19V13.18C18.4 13.6 18 14.3 18 15V20H6V15C6 14.3 5.6 13.6 5 13.18Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <h3 className="resume__section-title">Education</h3>
              </div>
              <div className="resume__timeline">
                <div className="resume__item">
                  <div className="resume__item-header">
                    <h4 className="resume__item-title">BS Computer Science</h4>
                    <span className="resume__item-date">2015 - 2016</span>
                  </div>
                  <h5 className="resume__item-company">
                    Abasyn University Peshawer
                  </h5>
                 
                </div>
                
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Resume;
